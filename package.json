{"name": "nobi-site", "version": "1.0.0", "description": "> 🚀 **Modern Portfolio Website** - Built with performance and professional standards in mind", "homepage": "https://github.com/Nobhokleng/nobi-site#readme", "bugs": {"url": "https://github.com/Nobhokleng/nobi-site/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Nobhokleng/nobi-site.git"}, "license": "MIT", "author": "", "type": "module", "main": "postcss.config.js", "directories": {"doc": "docs"}, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "check": "astro check"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^2.0.0", "@astrojs/tailwind": "^5.0.0", "astro": "^4.0.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^20.16.11", "typescript": "^5.8.3"}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.44.1"}}